<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 مولد QR Code - محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .input-group label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        #urlInput {
            direction: ltr;
            text-align: left;
        }

        .qr-display {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-display img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .qr-placeholder {
            color: #ccc;
            font-size: 1.1rem;
        }

        .qr-placeholder i {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }

        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .btn-social {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); }
        .btn-instagram { background: linear-gradient(135deg, #e1306c 0%, #fd1d1d 100%); }
        .btn-twitter { background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); }
        .btn-facebook { background: linear-gradient(135deg, #4267b2 0%, #365899 100%); }

        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        }

        .marketing-text {
            margin-top: 20px;
            padding: 20px;
            background: rgba(37, 211, 102, 0.1);
            border-radius: 15px;
            text-align: right;
        }

        .marketing-text h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .text-box {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .text-box:hover {
            background: #f0f8ff;
            transform: translateY(-2px);
        }

        .text-box p {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
            
            .social-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-qrcode"></i> مولد QR Code</h1>
            <p>محل خياطة السيدة آيه</p>
        </div>

        <div class="input-group">
            <label for="urlInput"><i class="fas fa-link"></i> رابط التطبيق:</label>
            <input type="url" id="urlInput" value="">
        </div>

        <div class="input-group">
            <label for="qrSize"><i class="fas fa-expand-arrows-alt"></i> حجم QR Code:</label>
            <select id="qrSize">
                <option value="200">صغير - 200px</option>
                <option value="300" selected>متوسط - 300px</option>
                <option value="400">كبير - 400px</option>
            </select>
        </div>

        <div class="qr-display" id="qrcode">
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                اضغط "إنشاء QR Code" لإنشاء الكود
            </div>
        </div>

        <div class="buttons">
            <button type="button" class="btn btn-primary" onclick="generateQR()">
                <i class="fas fa-sync-alt"></i>
                إنشاء QR Code
            </button>
            <button type="button" class="btn btn-success" onclick="downloadQR()">
                <i class="fas fa-download"></i>
                تحميل QR Code
            </button>
        </div>

        <div class="social-buttons">
            <button type="button" class="btn-social btn-whatsapp" onclick="shareToWhatsApp()">
                <i class="fab fa-whatsapp"></i>
                واتساب
            </button>
            <button type="button" class="btn-social btn-instagram" onclick="shareToInstagram()">
                <i class="fab fa-instagram"></i>
                انستقرام
            </button>
            <button type="button" class="btn-social btn-twitter" onclick="shareToTwitter()">
                <i class="fab fa-twitter"></i>
                تويتر
            </button>
            <button type="button" class="btn-social btn-facebook" onclick="shareToFacebook()">
                <i class="fab fa-facebook"></i>
                فيسبوك
            </button>
        </div>

        <div class="marketing-text">
            <h3><i class="fas fa-edit"></i> نص تسويقي جاهز</h3>
            <div class="text-box" onclick="copyText(this)">
                <p>🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965</p>
            </div>
        </div>
    </div>

    <div id="notification" class="notification">
        تم بنجاح! ✅
    </div>

    <script>
        // الرابط الافتراضي
        const defaultUrl = window.location.href.replace('qr-final.html', 'app.html');

        // تهيئة التطبيق
        window.addEventListener('DOMContentLoaded', () => {
            document.getElementById('urlInput').value = defaultUrl;
        });

        function generateQR() {
            const url = document.getElementById('urlInput').value || defaultUrl;
            const size = document.getElementById('qrSize').value;
            const qrContainer = document.getElementById('qrcode');
            
            // إظهار رسالة التحميل
            qrContainer.innerHTML = `
                <div class="qr-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري إنشاء QR Code...
                </div>
            `;
            
            // استخدام خدمة QR Code بسيطة وموثوقة
            const qrUrl = `https://chart.googleapis.com/chart?chs=${size}x${size}&cht=qr&chl=${encodeURIComponent(url)}`;
            
            // إنشاء صورة
            const img = new Image();
            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                showNotification('تم إنشاء QR Code بنجاح! ✅');
            };
            img.onerror = function() {
                qrContainer.innerHTML = `
                    <div class="qr-placeholder" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في إنشاء QR Code<br>
                        تحقق من الاتصال بالإنترنت
                    </div>
                `;
                showNotification('خطأ في إنشاء QR Code! ❌');
            };
            img.src = qrUrl;
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.borderRadius = '10px';
        }

        function downloadQR() {
            const img = document.querySelector('#qrcode img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'محل-خياطة-السيدة-آيه-QR.png';
                link.href = img.src;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showNotification('تم تحميل QR Code بنجاح! 📥');
            } else {
                showNotification('يرجى إنشاء QR Code أولاً! ⚠️');
            }
        }

        function shareToWhatsApp() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965

${document.getElementById('urlInput').value}`;
            
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
            showNotification('تم فتح واتساب للمشاركة! 📱');
        }

        function shareToInstagram() {
            showNotification('احفظ QR Code ثم شاركه في قصتك على انستقرام! 📸');
            downloadQR();
        }

        function shareToTwitter() {
            const text = `🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة

${document.getElementById('urlInput').value}`;
            
            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
            window.open(twitterUrl, '_blank');
            showNotification('تم فتح تويتر للمشاركة! 🐦');
        }

        function shareToFacebook() {
            const url = document.getElementById('urlInput').value;
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(facebookUrl, '_blank');
            showNotification('تم فتح فيسبوك للمشاركة! 📘');
        }

        function copyText(element) {
            const text = element.querySelector('p').textContent;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('تم نسخ النص التسويقي! 📋');
                    element.style.background = '#d4edda';
                    setTimeout(() => {
                        element.style.background = 'white';
                    }, 1000);
                }).catch(() => {
                    fallbackCopyText(text, element);
                });
            } else {
                fallbackCopyText(text, element);
            }
        }

        function fallbackCopyText(text, element) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showNotification('تم نسخ النص التسويقي! 📋');
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = 'white';
                }, 1000);
            } catch (err) {
                showNotification('فشل في نسخ النص! ❌');
            }
            document.body.removeChild(textArea);
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
