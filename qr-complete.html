<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 مولد QR Code - محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .input-group label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        #urlInput {
            direction: ltr;
            text-align: left;
        }

        .qr-display {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-display canvas {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .qr-placeholder {
            color: #ccc;
            font-size: 1.1rem;
        }

        .qr-placeholder i {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }

        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .btn-social {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); }
        .btn-instagram { background: linear-gradient(135deg, #e1306c 0%, #fd1d1d 100%); }
        .btn-twitter { background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); }
        .btn-facebook { background: linear-gradient(135deg, #4267b2 0%, #365899 100%); }

        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        }

        .marketing-text {
            margin-top: 20px;
            padding: 20px;
            background: rgba(37, 211, 102, 0.1);
            border-radius: 15px;
            text-align: right;
        }

        .marketing-text h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .text-box {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .text-box:hover {
            background: #f0f8ff;
            transform: translateY(-2px);
        }

        .text-box p {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
            
            .social-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-qrcode"></i> مولد QR Code</h1>
            <p>محل خياطة السيدة آيه - QR Code كامل ومتقن</p>
        </div>

        <div class="input-group">
            <label for="urlInput"><i class="fas fa-link"></i> رابط التطبيق:</label>
            <input type="url" id="urlInput" value="">
        </div>

        <div class="input-group">
            <label for="qrSize"><i class="fas fa-expand-arrows-alt"></i> حجم QR Code:</label>
            <select id="qrSize">
                <option value="200">صغير - 200px</option>
                <option value="300" selected>متوسط - 300px</option>
                <option value="400">كبير - 400px</option>
            </select>
        </div>

        <div class="qr-display" id="qrcode">
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                اضغط "إنشاء QR Code" لإنشاء الكود الكامل
            </div>
        </div>

        <div class="buttons">
            <button type="button" class="btn btn-primary" onclick="generateQR()">
                <i class="fas fa-sync-alt"></i>
                إنشاء QR Code
            </button>
            <button type="button" class="btn btn-success" onclick="downloadQR()">
                <i class="fas fa-download"></i>
                تحميل QR Code
            </button>
        </div>

        <div class="social-buttons">
            <button type="button" class="btn-social btn-whatsapp" onclick="shareToWhatsApp()">
                <i class="fab fa-whatsapp"></i>
                واتساب
            </button>
            <button type="button" class="btn-social btn-instagram" onclick="shareToInstagram()">
                <i class="fab fa-instagram"></i>
                انستقرام
            </button>
            <button type="button" class="btn-social btn-twitter" onclick="shareToTwitter()">
                <i class="fab fa-twitter"></i>
                تويتر
            </button>
            <button type="button" class="btn-social btn-facebook" onclick="shareToFacebook()">
                <i class="fab fa-facebook"></i>
                فيسبوك
            </button>
        </div>

        <div class="marketing-text">
            <h3><i class="fas fa-edit"></i> نص تسويقي جاهز</h3>
            <div class="text-box" onclick="copyText(this)">
                <p>🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965</p>
            </div>
        </div>
    </div>

    <div id="notification" class="notification">
        تم بنجاح! ✅
    </div>

    <!-- مكتبة QR Code كاملة ومحسنة -->
    <script>
        // مكتبة QR Code كاملة
        class QRCodeGenerator {
            constructor() {
                this.modules = [];
                this.moduleCount = 0;
            }

            generate(text, size) {
                // تحديد حجم الشبكة بناءً على طول النص
                this.moduleCount = this.getModuleCount(text.length);
                this.modules = this.createMatrix(this.moduleCount);

                // إضافة العناصر الأساسية
                this.addFinderPatterns();
                this.addSeparators();
                this.addTimingPatterns();
                this.addDarkModule();
                this.addFormatInfo();
                this.addData(text);

                // إنشاء Canvas
                return this.createCanvas(size);
            }

            getModuleCount(textLength) {
                if (textLength <= 10) return 21;
                if (textLength <= 20) return 25;
                if (textLength <= 35) return 29;
                return 33;
            }

            createMatrix(size) {
                const matrix = [];
                for (let i = 0; i < size; i++) {
                    matrix[i] = new Array(size).fill(false);
                }
                return matrix;
            }

            addFinderPatterns() {
                const pattern = [
                    [1,1,1,1,1,1,1],
                    [1,0,0,0,0,0,1],
                    [1,0,1,1,1,0,1],
                    [1,0,1,1,1,0,1],
                    [1,0,1,1,1,0,1],
                    [1,0,0,0,0,0,1],
                    [1,1,1,1,1,1,1]
                ];

                // الزاوية العلوية اليسرى
                this.addPattern(pattern, 0, 0);
                // الزاوية العلوية اليمنى
                this.addPattern(pattern, 0, this.moduleCount - 7);
                // الزاوية السفلية اليسرى
                this.addPattern(pattern, this.moduleCount - 7, 0);
            }

            addPattern(pattern, startRow, startCol) {
                for (let i = 0; i < pattern.length; i++) {
                    for (let j = 0; j < pattern[i].length; j++) {
                        if (startRow + i < this.moduleCount && startCol + j < this.moduleCount) {
                            this.modules[startRow + i][startCol + j] = pattern[i][j] === 1;
                        }
                    }
                }
            }

            addSeparators() {
                // إضافة الفواصل حول مربعات الزوايا
                for (let i = 0; i < 8; i++) {
                    // حول الزاوية العلوية اليسرى
                    if (i < 7) this.modules[7][i] = false;
                    if (i < 7) this.modules[i][7] = false;

                    // حول الزاوية العلوية اليمنى
                    if (i < 7) this.modules[7][this.moduleCount - 1 - i] = false;
                    if (i < 7) this.modules[i][this.moduleCount - 8] = false;

                    // حول الزاوية السفلية اليسرى
                    if (i < 7) this.modules[this.moduleCount - 8][i] = false;
                    if (i < 7) this.modules[this.moduleCount - 1 - i][7] = false;
                }
            }

            addTimingPatterns() {
                // خطوط التوقيت الأفقية والعمودية
                for (let i = 8; i < this.moduleCount - 8; i++) {
                    this.modules[6][i] = i % 2 === 0;
                    this.modules[i][6] = i % 2 === 0;
                }
            }

            addDarkModule() {
                // النقطة المظلمة الثابتة
                this.modules[4 * 7 + 9][8] = true;
            }

            addFormatInfo() {
                // إضافة معلومات التنسيق (مبسطة)
                const formatBits = [1,0,1,0,1,0,0,0,0,0,1,0,0,1,0];

                // أفقي
                for (let i = 0; i < 6; i++) {
                    this.modules[8][i] = formatBits[i];
                }
                this.modules[8][7] = formatBits[6];
                this.modules[8][8] = formatBits[7];
                for (let i = 7; i < 15; i++) {
                    this.modules[8][this.moduleCount - 15 + i] = formatBits[i];
                }

                // عمودي
                for (let i = 0; i < 8; i++) {
                    this.modules[i][8] = formatBits[14 - i];
                }
                for (let i = 8; i < 15; i++) {
                    this.modules[this.moduleCount - 15 + i][8] = formatBits[14 - i];
                }
            }

            addData(text) {
                // تحويل النص إلى بيانات
                const data = this.encodeData(text);

                // إضافة البيانات في نمط متعرج
                let bitIndex = 0;
                let direction = -1; // -1 للأعلى، 1 للأسفل

                for (let col = this.moduleCount - 1; col > 0; col -= 2) {
                    if (col === 6) col--; // تجاوز عمود التوقيت

                    for (let count = 0; count < this.moduleCount; count++) {
                        for (let c = 0; c < 2; c++) {
                            const currentCol = col - c;
                            const row = direction === -1 ? this.moduleCount - 1 - count : count;

                            if (!this.isReserved(row, currentCol) && bitIndex < data.length) {
                                this.modules[row][currentCol] = data[bitIndex];
                                bitIndex++;
                            }
                        }
                    }
                    direction *= -1;
                }
            }

            encodeData(text) {
                // تحويل النص إلى مصفوفة بت
                const data = [];

                // إضافة رأس البيانات
                const modeIndicator = [0,1,0,0]; // وضع البايت
                const charCount = this.toBinary(text.length, 8);

                data.push(...modeIndicator);
                data.push(...charCount);

                // إضافة بيانات النص
                for (let i = 0; i < text.length; i++) {
                    const charCode = text.charCodeAt(i);
                    data.push(...this.toBinary(charCode, 8));
                }

                // إضافة بت الإنهاء
                for (let i = 0; i < 4 && data.length < this.getDataCapacity(); i++) {
                    data.push(0);
                }

                // ملء البيانات المتبقية
                while (data.length < this.getDataCapacity()) {
                    data.push(...[1,1,1,0,1,1,0,0]); // 236
                    if (data.length < this.getDataCapacity()) {
                        data.push(...[0,0,0,1,0,0,0,1]); // 17
                    }
                }

                return data.slice(0, this.getDataCapacity());
            }

            toBinary(num, length) {
                return num.toString(2).padStart(length, '0').split('').map(b => b === '1');
            }

            getDataCapacity() {
                // سعة البيانات بناءً على حجم الشبكة
                const totalModules = this.moduleCount * this.moduleCount;
                const functionModules = 208; // تقدير للوحدات الوظيفية
                return Math.floor((totalModules - functionModules) * 0.8);
            }

            isReserved(row, col) {
                // فحص ما إذا كانت الخلية محجوزة للوظائف
                if (row < 0 || row >= this.moduleCount || col < 0 || col >= this.moduleCount) {
                    return true;
                }

                // مربعات الزوايا
                if ((row < 9 && col < 9) ||
                    (row < 9 && col >= this.moduleCount - 8) ||
                    (row >= this.moduleCount - 8 && col < 9)) {
                    return true;
                }

                // خطوط التوقيت
                if (row === 6 || col === 6) {
                    return true;
                }

                // معلومات التنسيق
                if ((row === 8 && (col < 9 || col >= this.moduleCount - 8)) ||
                    (col === 8 && (row < 9 || row >= this.moduleCount - 7))) {
                    return true;
                }

                return false;
            }

            createCanvas(size) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = size;
                canvas.height = size;

                const cellSize = size / this.moduleCount;

                // خلفية بيضاء
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);

                // رسم الوحدات
                ctx.fillStyle = '#000000';
                for (let row = 0; row < this.moduleCount; row++) {
                    for (let col = 0; col < this.moduleCount; col++) {
                        if (this.modules[row][col]) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                return canvas;
            }
        }

        // الرابط الافتراضي
        const defaultUrl = window.location.href.replace('qr-complete.html', 'app.html');
        const qrGenerator = new QRCodeGenerator();

        // تهيئة التطبيق
        window.addEventListener('DOMContentLoaded', () => {
            document.getElementById('urlInput').value = defaultUrl;
            generateQR(); // إنشاء QR Code تلقائياً
        });

        function generateQR() {
            const url = document.getElementById('urlInput').value || defaultUrl;
            const size = parseInt(document.getElementById('qrSize').value);
            const qrContainer = document.getElementById('qrcode');

            try {
                // إظهار رسالة التحميل
                qrContainer.innerHTML = `
                    <div class="qr-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري إنشاء QR Code الكامل...
                    </div>
                `;

                // إنشاء QR Code كامل
                setTimeout(() => {
                    const canvas = qrGenerator.generate(url, size);
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(canvas);
                    showNotification('تم إنشاء QR Code كامل بنجاح! ✅');
                }, 500);

            } catch (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                qrContainer.innerHTML = `
                    <div class="qr-placeholder" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في إنشاء QR Code
                    </div>
                `;
                showNotification('خطأ في إنشاء QR Code! ❌');
            }
        }

        function downloadQR() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = 'محل-خياطة-السيدة-آيه-QR-كامل.png';
                link.href = canvas.toDataURL('image/png');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showNotification('تم تحميل QR Code الكامل بنجاح! 📥');
            } else {
                showNotification('يرجى إنشاء QR Code أولاً! ⚠️');
            }
        }

        function shareToWhatsApp() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965

${document.getElementById('urlInput').value}`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
            showNotification('تم فتح واتساب للمشاركة! 📱');
        }

        function shareToInstagram() {
            showNotification('احفظ QR Code ثم شاركه في قصتك على انستقرام! 📸');
            downloadQR();
        }

        function shareToTwitter() {
            const text = `🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة

${document.getElementById('urlInput').value}`;

            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
            window.open(twitterUrl, '_blank');
            showNotification('تم فتح تويتر للمشاركة! 🐦');
        }

        function shareToFacebook() {
            const url = document.getElementById('urlInput').value;
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(facebookUrl, '_blank');
            showNotification('تم فتح فيسبوك للمشاركة! 📘');
        }

        function copyText(element) {
            const text = element.querySelector('p').textContent;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('تم نسخ النص التسويقي! 📋');
                    element.style.background = '#d4edda';
                    setTimeout(() => {
                        element.style.background = 'white';
                    }, 1000);
                }).catch(() => {
                    fallbackCopyText(text, element);
                });
            } else {
                fallbackCopyText(text, element);
            }
        }

        function fallbackCopyText(text, element) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showNotification('تم نسخ النص التسويقي! 📋');
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = 'white';
                }, 1000);
            } catch (err) {
                showNotification('فشل في نسخ النص! ❌');
            }
            document.body.removeChild(textArea);
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('qrSize').addEventListener('change', generateQR);
            document.getElementById('urlInput').addEventListener('input', function() {
                if (this.value.trim()) {
                    generateQR();
                }
            });
        });
    </script>
</body>
</html>
