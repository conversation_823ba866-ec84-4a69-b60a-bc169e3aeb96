<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 مولد QR Code - محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .panel {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 25px;
        }

        .section-title {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.3);
        }

        #urlInput {
            direction: ltr;
            text-align: left;
        }

        .qr-display {
            text-align: center;
        }

        #qrcode {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-height: 280px;
        }

        .qr-placeholder {
            color: #ccc;
            font-size: 1.1rem;
            text-align: center;
        }

        .qr-placeholder i {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }

        .qr-info {
            margin-top: 15px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            color: #666;
            font-size: 0.9rem;
            text-align: center;
        }

        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .social-share {
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(18, 140, 126, 0.1) 100%);
            border-radius: 15px;
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-top: 20px;
        }

        .btn-social {
            padding: 12px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); }
        .btn-instagram { background: linear-gradient(135deg, #e1306c 0%, #fd1d1d 100%); }
        .btn-twitter { background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); }
        .btn-facebook { background: linear-gradient(135deg, #4267b2 0%, #365899 100%); }
        .btn-telegram { background: linear-gradient(135deg, #0088cc 0%, #006699 100%); }
        .btn-copy { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }

        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .marketing-texts {
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
            border-radius: 15px;
        }

        .text-templates {
            display: grid;
            gap: 15px;
            margin-top: 20px;
        }

        .template {
            background: white;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .template:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .template h4 {
            color: #333;
            margin-bottom: 12px;
            font-size: 1.1rem;
        }

        .template p {
            color: #666;
            line-height: 1.7;
            margin: 0;
            text-align: right;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
            
            .social-buttons {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-qrcode"></i> مولد QR Code للتواصل الاجتماعي</h1>
            <p>محل خياطة السيدة آيه - انشر بسرعة وسهولة!</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Settings Panel -->
            <div class="panel">
                <h3 class="section-title">
                    <i class="fas fa-cog"></i>
                    إعدادات QR Code
                </h3>

                <div class="input-group">
                    <label for="urlInput"><i class="fas fa-link"></i> رابط التطبيق:</label>
                    <input type="url" id="urlInput" placeholder="https://example.com/app.html">
                </div>

                <div class="input-group">
                    <label for="qrSize"><i class="fas fa-expand-arrows-alt"></i> حجم QR Code:</label>
                    <select id="qrSize">
                        <option value="200">صغير - 200px (للمنشورات)</option>
                        <option value="300" selected>متوسط - 300px (للقصص)</option>
                        <option value="400">كبير - 400px (للطباعة)</option>
                        <option value="500">كبير جداً - 500px (للبانرات)</option>
                    </select>
                </div>

                <div class="input-group">
                    <label for="qrColor"><i class="fas fa-palette"></i> لون QR Code:</label>
                    <select id="qrColor">
                        <option value="#333333">⚫ أسود كلاسيكي</option>
                        <option value="#667eea" selected>🔵 أزرق أنيق</option>
                        <option value="#25d366">🟢 أخضر واتساب</option>
                        <option value="#e74c3c">🔴 أحمر جذاب</option>
                        <option value="#9b59b6">🟣 بنفسجي ملكي</option>
                        <option value="#f39c12">🟡 ذهبي فاخر</option>
                    </select>
                </div>

                <div class="buttons">
                    <button type="button" class="btn btn-primary" onclick="generateQR()">
                        <i class="fas fa-sync-alt"></i>
                        إنشاء QR Code
                    </button>
                    <button type="button" class="btn btn-success" onclick="downloadQR()">
                        <i class="fas fa-download"></i>
                        تحميل QR Code
                    </button>
                </div>
            </div>

            <!-- QR Display Panel -->
            <div class="panel">
                <h3 class="section-title">
                    <i class="fas fa-eye"></i>
                    معاينة QR Code
                </h3>

                <div class="qr-display">
                    <div id="qrcode">
                        <div class="qr-placeholder">
                            <i class="fas fa-qrcode"></i>
                            سيظهر QR Code هنا
                        </div>
                    </div>
                    <div class="qr-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="qrDimensions">الحجم: 300 × 300 بكسل</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media Sharing -->
        <div class="social-share">
            <h3 class="section-title">
                <i class="fas fa-share-alt"></i>
                مشاركة مباشرة في مواقع التواصل
            </h3>
            <div class="social-buttons">
                <button type="button" class="btn-social btn-whatsapp" onclick="shareToWhatsApp()">
                    <i class="fab fa-whatsapp"></i>
                    واتساب
                </button>
                <button type="button" class="btn-social btn-instagram" onclick="shareToInstagram()">
                    <i class="fab fa-instagram"></i>
                    انستقرام
                </button>
                <button type="button" class="btn-social btn-twitter" onclick="shareToTwitter()">
                    <i class="fab fa-twitter"></i>
                    تويتر
                </button>
                <button type="button" class="btn-social btn-facebook" onclick="shareToFacebook()">
                    <i class="fab fa-facebook"></i>
                    فيسبوك
                </button>
                <button type="button" class="btn-social btn-telegram" onclick="shareToTelegram()">
                    <i class="fab fa-telegram"></i>
                    تيليجرام
                </button>
                <button type="button" class="btn-social btn-copy" onclick="copyQRImage()">
                    <i class="fas fa-copy"></i>
                    نسخ الصورة
                </button>
            </div>
        </div>

        <!-- Marketing Text Templates -->
        <div class="marketing-texts">
            <h3 class="section-title">
                <i class="fas fa-edit"></i>
                نصوص تسويقية جاهزة للنسخ
            </h3>
            <div class="text-templates">
                <div class="template" onclick="copyText(this)">
                    <h4><i class="fab fa-whatsapp"></i> للواتساب والتيليجرام:</h4>
                    <p>🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب والاستفسار: 0561556965</p>
                </div>

                <div class="template" onclick="copyText(this)">
                    <h4><i class="fab fa-instagram"></i> للانستقرام والفيسبوك:</h4>
                    <p>✨ فساتين راقية من محل السيدة آيه ✨
🎨 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح QR Code واطلب بسهولة!
🌟 جودة عالية وخدمة مميزة
#فساتين #خياطة #السيدة_آيه #موضة #تصميم #أناقة</p>
                </div>

                <div class="template" onclick="copyText(this)">
                    <h4><i class="fab fa-twitter"></i> لتويتر:</h4>
                    <p>🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة #أناقة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <i class="fas fa-check-circle"></i>
        <span>تم بنجاح!</span>
    </div>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

    <script>
        // متغيرات عامة
        let qrCodeInstance = null;
        const defaultUrl = window.location.href.replace('qr-generator-fixed.html', 'app.html');

        // تهيئة التطبيق
        window.addEventListener('DOMContentLoaded', () => {
            initializeApp();
        });

        function initializeApp() {
            // تعيين الرابط الافتراضي
            document.getElementById('urlInput').value = defaultUrl;

            // إنشاء QR Code تلقائياً
            generateQR();

            // إعداد مستمعي الأحداث
            setupEventListeners();
        }

        function setupEventListeners() {
            // تحديث QR Code عند تغيير الخيارات
            document.getElementById('qrSize').addEventListener('change', generateQR);
            document.getElementById('qrColor').addEventListener('change', generateQR);

            // تحديث QR Code عند تغيير الرابط
            document.getElementById('urlInput').addEventListener('input', function() {
                if (this.value.trim()) {
                    generateQR();
                }
            });
        }

        function generateQR() {
            const url = document.getElementById('urlInput').value || defaultUrl;
            const size = parseInt(document.getElementById('qrSize').value);
            const color = document.getElementById('qrColor').value;
            const qrContainer = document.getElementById('qrcode');

            // تحديث معلومات الحجم
            document.getElementById('qrDimensions').innerHTML =
                `<i class="fas fa-info-circle"></i> الحجم: ${size} × ${size} بكسل`;

            try {
                // مسح المحتوى السابق
                qrContainer.innerHTML = '';

                // إنشاء عنصر canvas
                const canvas = document.createElement('canvas');
                qrContainer.appendChild(canvas);

                // إنشاء QR Code باستخدام QRious
                qrCodeInstance = new QRious({
                    element: canvas,
                    value: url,
                    size: size,
                    foreground: color,
                    background: '#ffffff',
                    level: 'M'
                });

                console.log('تم إنشاء QR Code بنجاح!');
                showNotification('تم إنشاء QR Code بنجاح!', 'fas fa-check-circle');

            } catch (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                qrContainer.innerHTML = `
                    <div class="qr-placeholder" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في إنشاء QR Code
                    </div>
                `;
                showNotification('خطأ في إنشاء QR Code!', 'fas fa-exclamation-triangle');
            }
        }

        function downloadQR() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                const size = document.getElementById('qrSize').value;
                const color = document.getElementById('qrColor').value.replace('#', '');
                const timestamp = new Date().toISOString().slice(0, 10);

                const link = document.createElement('a');
                link.download = `محل-خياطة-السيدة-آيه-QR-${size}px-${color}-${timestamp}.png`;
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                showNotification('تم تحميل QR Code بنجاح!', 'fas fa-download');
            } else {
                showNotification('يرجى إنشاء QR Code أولاً!', 'fas fa-exclamation-triangle');
            }
        }

        // وظائف المشاركة في مواقع التواصل
        function shareToWhatsApp() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب والاستفسار: 0561556965

${document.getElementById('urlInput').value}`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
            showNotification('تم فتح واتساب للمشاركة!', 'fab fa-whatsapp');
        }

        function shareToInstagram() {
            showNotification('احفظ QR Code ثم شاركه في قصتك على انستقرام!', 'fab fa-instagram');
            downloadQR();
        }

        function shareToTwitter() {
            const text = `🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة #أناقة

${document.getElementById('urlInput').value}`;

            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
            window.open(twitterUrl, '_blank');
            showNotification('تم فتح تويتر للمشاركة!', 'fab fa-twitter');
        }

        function shareToFacebook() {
            const url = document.getElementById('urlInput').value;
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(facebookUrl, '_blank');
            showNotification('تم فتح فيسبوك للمشاركة!', 'fab fa-facebook');
        }

        function shareToTelegram() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
📱 امسح الكود واطلب الآن 👇

${document.getElementById('urlInput').value}`;

            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(document.getElementById('urlInput').value)}&text=${encodeURIComponent(text)}`;
            window.open(telegramUrl, '_blank');
            showNotification('تم فتح تيليجرام للمشاركة!', 'fab fa-telegram');
        }

        async function copyQRImage() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                try {
                    canvas.toBlob(async (blob) => {
                        const item = new ClipboardItem({ 'image/png': blob });
                        await navigator.clipboard.write([item]);
                        showNotification('تم نسخ صورة QR Code!', 'fas fa-copy');
                    });
                } catch (err) {
                    // إذا فشل نسخ الصورة، نسخ الرابط بدلاً من ذلك
                    await navigator.clipboard.writeText(document.getElementById('urlInput').value);
                    showNotification('تم نسخ الرابط!', 'fas fa-link');
                }
            } else {
                showNotification('يرجى إنشاء QR Code أولاً!', 'fas fa-exclamation-triangle');
            }
        }

        // نسخ النصوص التسويقية
        async function copyText(element) {
            const text = element.querySelector('p').textContent;
            try {
                await navigator.clipboard.writeText(text);
                showNotification('تم نسخ النص التسويقي!', 'fas fa-edit');

                // تأثير بصري للنسخ
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = 'white';
                }, 1000);
            } catch (err) {
                console.error('فشل في نسخ النص:', err);
                showNotification('فشل في نسخ النص!', 'fas fa-exclamation-triangle');
            }
        }

        // إظهار الإشعارات
        function showNotification(message, icon = 'fas fa-check-circle') {
            const notification = document.getElementById('notification');
            notification.innerHTML = `<i class="${icon}"></i><span>${message}</span>`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
