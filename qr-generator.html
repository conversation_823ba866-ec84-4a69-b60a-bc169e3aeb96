<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد QR Code - محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .qr-section {
            margin: 30px 0;
        }

        #qrcode {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .input-section {
            margin: 20px 0;
        }

        .input-section label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: right;
        }

        .input-section input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            direction: ltr;
            text-align: left;
        }

        .input-section input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            min-width: 120px;
        }

        .btn-generate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-download {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-whatsapp:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
            text-decoration: none;
            color: white;
        }

        .info-box {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .info-box h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .info-box p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .customization-section {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .customization-section h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .option-group {
            text-align: right;
        }

        .option-group label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .option-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            background: white;
        }

        .option-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .qr-info {
            margin-top: 10px;
            color: #666;
            font-size: 0.9rem;
        }

        .social-share {
            margin: 30px 0;
            padding: 20px;
            background: rgba(37, 211, 102, 0.1);
            border-radius: 15px;
        }

        .social-share h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .btn-social {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            color: white;
        }

        .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); }
        .btn-instagram { background: linear-gradient(135deg, #e1306c 0%, #fd1d1d 100%); }
        .btn-twitter { background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); }
        .btn-facebook { background: linear-gradient(135deg, #4267b2 0%, #365899 100%); }
        .btn-telegram { background: linear-gradient(135deg, #0088cc 0%, #006699 100%); }
        .btn-copy { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }

        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .marketing-texts {
            margin: 30px 0;
            padding: 20px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 15px;
        }

        .marketing-texts h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .text-templates {
            display: grid;
            gap: 15px;
        }

        .template {
            background: white;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .template:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .template h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .template p {
            color: #666;
            line-height: 1.6;
            margin: 0;
            text-align: right;
        }

        .copy-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .copy-notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                max-width: 100%;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .options-grid {
                grid-template-columns: 1fr;
            }

            .social-buttons {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 مولد QR Code للتواصل الاجتماعي</h1>
            <p>محل خياطة السيدة آيه - انشر بسرعة وسهولة!</p>
        </div>

        <div class="input-section">
            <label for="urlInput">رابط التطبيق:</label>
            <input type="url" id="urlInput" placeholder="https://example.com/app.html" value="">
        </div>

        <!-- خيارات التخصيص -->
        <div class="customization-section">
            <h3>🎨 خيارات التصميم:</h3>
            <div class="options-grid">
                <div class="option-group">
                    <label>حجم QR Code:</label>
                    <select id="qrSize">
                        <option value="200">صغير - 200px (للمنشورات)</option>
                        <option value="300" selected>متوسط - 300px (للقصص)</option>
                        <option value="400">كبير - 400px (للطباعة)</option>
                        <option value="500">كبير جداً - 500px (للبانرات)</option>
                    </select>
                </div>
                <div class="option-group">
                    <label>لون QR Code:</label>
                    <select id="qrColor">
                        <option value="#333333">أسود كلاسيكي</option>
                        <option value="#667eea" selected>أزرق أنيق</option>
                        <option value="#25d366">أخضر واتساب</option>
                        <option value="#e74c3c">أحمر جذاب</option>
                        <option value="#9b59b6">بنفسجي ملكي</option>
                        <option value="#f39c12">ذهبي فاخر</option>
                    </select>
                </div>
                <div class="option-group">
                    <label>شكل النقاط:</label>
                    <select id="qrStyle">
                        <option value="square">مربعات</option>
                        <option value="dots" selected>نقاط دائرية</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="qr-section">
            <div id="qrcode"></div>
            <div class="qr-info">
                <p id="qrDimensions">الحجم: 300 × 300 بكسل</p>
            </div>
        </div>

        <div class="buttons">
            <button type="button" class="btn btn-generate" onclick="generateQR()">🔄 إنشاء QR Code</button>
            <button type="button" class="btn btn-download" onclick="downloadQR()">📥 تحميل QR Code</button>
        </div>

        <!-- أزرار المشاركة المباشرة -->
        <div class="social-share">
            <h3>📱 مشاركة مباشرة في:</h3>
            <div class="social-buttons">
                <button type="button" class="btn-social btn-whatsapp" onclick="shareToWhatsApp()">
                    📱 واتساب
                </button>
                <button type="button" class="btn-social btn-instagram" onclick="shareToInstagram()">
                    📸 انستقرام
                </button>
                <button type="button" class="btn-social btn-twitter" onclick="shareToTwitter()">
                    🐦 تويتر
                </button>
                <button type="button" class="btn-social btn-facebook" onclick="shareToFacebook()">
                    📘 فيسبوك
                </button>
                <button type="button" class="btn-social btn-telegram" onclick="shareToTelegram()">
                    ✈️ تيليجرام
                </button>
                <button type="button" class="btn-social btn-copy" onclick="copyQRImage()">
                    📋 نسخ الصورة
                </button>
            </div>
        </div>

        <!-- نصوص تسويقية جاهزة -->
        <div class="marketing-texts">
            <h3>✍️ نصوص تسويقية جاهزة:</h3>
            <div class="text-templates">
                <div class="template" onclick="copyText(this)">
                    <h4>📱 للواتساب والتيليجرام:</h4>
                    <p>🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965</p>
                </div>
                <div class="template" onclick="copyText(this)">
                    <h4>📸 للانستقرام والفيسبوك:</h4>
                    <p>✨ فساتين راقية من محل السيدة آيه ✨
🎨 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح QR Code واطلب بسهولة!
#فساتين #خياطة #السيدة_آيه #موضة</p>
                </div>
                <div class="template" onclick="copyText(this)">
                    <h4>🐦 لتويتر:</h4>
                    <p>🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة</p>
                </div>
            </div>
        </div>

        <div class="info-box">
            <h3>📋 كيفية الاستخدام:</h3>
            <p>1. ضع رابط تطبيق محل خياطة السيدة آيه في الحقل أعلاه</p>
            <p>2. اضغط "إنشاء QR Code" لتوليد الكود</p>
            <p>3. اضغط "تحميل QR Code" لحفظ الصورة</p>
            <p>4. اطبع الصورة أو شاركها مع العملاء</p>
            <p>5. عندما يمسح العميل الكود، سيفتح التطبيق مباشرة!</p>
        </div>

        <div class="info-box">
            <h3>💡 نصائح للاستخدام:</h3>
            <p>• ضع QR Code في المحل، على بطاقات العمل، والإعلانات</p>
            <p>• شارك الكود في وسائل التواصل الاجتماعي</p>
            <p>• تأكد من أن الرابط يعمل قبل طباعة الكود</p>
            <p>• استخدم حجم مناسب للطباعة (على الأقل 3×3 سم)</p>
        </div>
    </div>

    <!-- إشعار النسخ -->
    <div id="copyNotification" class="copy-notification">
        تم النسخ بنجاح! ✅
    </div>

    <script>
        // رابط افتراضي (يمكن تغييره)
        const defaultUrl = window.location.href.replace('qr-generator.html', 'app.html');
        document.getElementById('urlInput').value = defaultUrl;

        // إنشاء QR Code تلقائياً عند تحميل الصفحة
        window.addEventListener('DOMContentLoaded', () => {
            generateQR();
            setupEventListeners();
        });

        function setupEventListeners() {
            // تحديث QR Code عند تغيير الخيارات
            document.getElementById('qrSize').addEventListener('change', generateQR);
            document.getElementById('qrColor').addEventListener('change', generateQR);
            document.getElementById('qrStyle').addEventListener('change', generateQR);
            document.getElementById('urlInput').addEventListener('input', function() {
                if (this.value.trim()) {
                    generateQR();
                }
            });
        }

        function generateQR() {
            const url = document.getElementById('urlInput').value || defaultUrl;
            const size = parseInt(document.getElementById('qrSize').value);
            const color = document.getElementById('qrColor').value;
            const style = document.getElementById('qrStyle').value;
            const qrContainer = document.getElementById('qrcode');

            // تحديث معلومات الحجم
            document.getElementById('qrDimensions').textContent = `الحجم: ${size} × ${size} بكسل`;

            // مسح QR Code السابق
            qrContainer.innerHTML = '';

            // إنشاء QR Code جديد
            const options = {
                width: size,
                height: size,
                colorDark: color,
                colorLight: '#ffffff',
                margin: 2,
                errorCorrectionLevel: 'M'
            };

            // إضافة نمط النقاط إذا تم اختياره
            if (style === 'dots') {
                options.rendererOpts = {
                    quality: 0.3
                };
            }

            QRCode.toCanvas(qrContainer, url, options, function (error) {
                if (error) {
                    console.error('خطأ في إنشاء QR Code:', error);
                    qrContainer.innerHTML = '<p style="color: red;">خطأ في إنشاء QR Code</p>';
                } else {
                    console.log('تم إنشاء QR Code بنجاح!');
                }
            });
        }

        function downloadQR() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                const size = document.getElementById('qrSize').value;
                const color = document.getElementById('qrColor').value.replace('#', '');
                const timestamp = new Date().toISOString().slice(0, 10);

                const link = document.createElement('a');
                link.download = `محل-خياطة-السيدة-آيه-QR-${size}px-${color}-${timestamp}.png`;
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                showNotification('تم تحميل QR Code بنجاح! 📥');
            } else {
                alert('يرجى إنشاء QR Code أولاً!');
            }
        }

        // وظائف المشاركة في مواقع التواصل
        function shareToWhatsApp() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965

${document.getElementById('urlInput').value}`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
        }

        function shareToInstagram() {
            showNotification('احفظ QR Code ثم شاركه في قصتك على انستقرام! 📸');
            downloadQR();
        }

        function shareToTwitter() {
            const text = `🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة

${document.getElementById('urlInput').value}`;

            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
            window.open(twitterUrl, '_blank');
        }

        function shareToFacebook() {
            const url = document.getElementById('urlInput').value;
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(facebookUrl, '_blank');
        }

        function shareToTelegram() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
امسح الكود واطلب الآن 👇

${document.getElementById('urlInput').value}`;

            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(document.getElementById('urlInput').value)}&text=${encodeURIComponent(text)}`;
            window.open(telegramUrl, '_blank');
        }

        async function copyQRImage() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                try {
                    canvas.toBlob(async (blob) => {
                        const item = new ClipboardItem({ 'image/png': blob });
                        await navigator.clipboard.write([item]);
                        showNotification('تم نسخ صورة QR Code! 📋');
                    });
                } catch (err) {
                    // إذا فشل نسخ الصورة، نسخ الرابط بدلاً من ذلك
                    await navigator.clipboard.writeText(document.getElementById('urlInput').value);
                    showNotification('تم نسخ الرابط! 📋');
                }
            }
        }

        // نسخ النصوص التسويقية
        async function copyText(element) {
            const text = element.querySelector('p').textContent;
            try {
                await navigator.clipboard.writeText(text);
                showNotification('تم نسخ النص التسويقي! ✅');
            } catch (err) {
                console.error('فشل في نسخ النص:', err);
            }
        }

        // إظهار الإشعارات
        function showNotification(message) {
            const notification = document.getElementById('copyNotification');
            notification.textContent = message;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
