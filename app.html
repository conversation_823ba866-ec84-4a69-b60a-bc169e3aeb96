<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
        }

        .section {
            margin-bottom: 50px;
        }

        .section-title {
            text-align: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .dress-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .dress-pink { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .dress-blue { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .dress-green { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        .dress-purple { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
        .dress-red { background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%); }
        .dress-yellow { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%); }

        .tool-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .dress-info, .tool-info {
            text-align: center;
        }

        .dress-info h3, .tool-info h3 {
            color: #333;
            font-size: 1.4rem;
            margin-bottom: 10px;
        }

        .dress-info p, .tool-info p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 20px;
        }

        .btn-order {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-family: 'Cairo', sans-serif;
        }

        .btn-order:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
        }

        .whatsapp-float {
            position: fixed;
            width: 60px;
            height: 60px;
            bottom: 40px;
            left: 40px;
            background: #25d366;
            color: white;
            border-radius: 50%;
            text-align: center;
            font-size: 30px;
            box-shadow: 2px 2px 3px #999;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .whatsapp-float:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 1.5rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 محل خياطة السيدة آيه 🌟</h1>
            <p>أجمل الفساتين وأدوات الخياطة بأفضل الأسعار</p>
        </div>

        <div class="section">
            <h2 class="section-title">👗 مجموعة الفساتين الراقية</h2>
            <div class="grid">
                <div class="card">
                    <div class="dress-img dress-pink"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>🌸 فستان وردي أنيق</h3>
                        <p>فستان راقي بتصميم عصري ولون وردي جذاب، مناسب للمناسبات الخاصة</p>
                        <div class="price">💰 250 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان وردي أنيق', '250')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="dress-img dress-blue"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>💙 فستان أزرق كلاسيكي</h3>
                        <p>فستان كلاسيكي بلون أزرق هادئ، مثالي للإطلالات اليومية الأنيقة</p>
                        <div class="price">💰 180 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان أزرق كلاسيكي', '180')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="dress-img dress-green"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>💚 فستان أخضر صيفي</h3>
                        <p>فستان صيفي منعش بلون أخضر طبيعي، مريح وأنيق للأيام الحارة</p>
                        <div class="price">💰 200 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان أخضر صيفي', '200')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="dress-img dress-purple"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>💜 فستان بنفسجي راقي</h3>
                        <p>فستان فاخر بلون بنفسجي ملكي، مصمم خصيصاً للمناسبات الرسمية</p>
                        <div class="price">💰 320 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان بنفسجي راقي', '320')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="dress-img dress-red"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>❤️ فستان أحمر جذاب</h3>
                        <p>فستان أحمر لافت ومميز، يضفي إطلالة جريئة وواثقة</p>
                        <div class="price">💰 280 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان أحمر جذاب', '280')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="dress-img dress-yellow"><i class="fas fa-tshirt"></i></div>
                    <div class="dress-info">
                        <h3>💛 فستان أصفر مشرق</h3>
                        <p>فستان مشرق بلون أصفر زاهي، يضفي البهجة والحيوية على إطلالتك</p>
                        <div class="price">💰 220 ريال</div>
                        <button class="btn-order" onclick="orderDress('فستان أصفر مشرق', '220')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧵 أدوات الخياطة الاحترافية</h2>
            <div class="grid">
                <div class="card">
                    <div class="tool-img"><i class="fas fa-cut"></i></div>
                    <div class="tool-info">
                        <h3>✂️ مقصات خياطة احترافية</h3>
                        <p>مقصات عالية الجودة مصنوعة من الستانلس ستيل، حادة ودقيقة للقص المثالي</p>
                        <div class="price">💰 45 ريال</div>
                        <button class="btn-order" onclick="orderTool('مقصات خياطة احترافية', '45')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="tool-img"><i class="fas fa-thumbtack"></i></div>
                    <div class="tool-info">
                        <h3>📌 دبابيس خياطة متنوعة</h3>
                        <p>مجموعة دبابيس خياطة بأحجام مختلفة، مقاومة للصدأ وسهلة الاستخدام</p>
                        <div class="price">💰 15 ريال</div>
                        <button class="btn-order" onclick="orderTool('دبابيس خياطة متنوعة', '15')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="tool-img"><i class="fas fa-ruler"></i></div>
                    <div class="tool-info">
                        <h3>📏 مسطرة قياس مرنة</h3>
                        <p>مسطرة قياس مرنة بطول 150 سم، مقاومة للتمزق ومطبوع عليها القياسات بوضوح</p>
                        <div class="price">💰 25 ريال</div>
                        <button class="btn-order" onclick="orderTool('مسطرة قياس مرنة', '25')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="tool-img"><i class="fas fa-circle"></i></div>
                    <div class="tool-info">
                        <h3>🧶 خيوط ملونة متنوعة</h3>
                        <p>مجموعة خيوط بألوان متنوعة وجودة عالية، مقاومة للتمزق ومناسبة لجميع الأقمشة</p>
                        <div class="price">💰 30 ريال</div>
                        <button class="btn-order" onclick="orderTool('خيوط ملونة متنوعة', '30')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="tool-img"><i class="fas fa-location-arrow"></i></div>
                    <div class="tool-info">
                        <h3>🪡 إبر خياطة بأحجام مختلفة</h3>
                        <p>مجموعة إبر خياطة بأحجام متنوعة، مصنوعة من الفولاذ المقاوم للصدأ</p>
                        <div class="price">💰 20 ريال</div>
                        <button class="btn-order" onclick="orderTool('إبر خياطة بأحجام مختلفة', '20')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>

                <div class="card">
                    <div class="tool-img"><i class="fas fa-shield-alt"></i></div>
                    <div class="tool-info">
                        <h3>🛡️ كشتبان معدني</h3>
                        <p>كشتبان معدني قوي ومريح، يحمي الإصبع أثناء الخياطة ويسهل عملية الدفع</p>
                        <div class="price">💰 12 ريال</div>
                        <button class="btn-order" onclick="orderTool('كشتبان معدني', '12')">📱 اطلب عبر واتساب</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- زر واتساب العائم -->
    <a href="https://wa.me/966561556965?text=السلام عليكم، أريد الاستفسار عن منتجات محل خياطة السيدة آيه" class="whatsapp-float" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        function orderDress(dressName, price) {
            const message = `السلام عليكم ورحمة الله وبركاته

أريد طلب: ${dressName}
السعر: ${price} ريال

من محل خياطة السيدة آيه 🌟`;

            const whatsappUrl = `https://wa.me/966561556965?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function orderTool(toolName, price) {
            const message = `السلام عليكم ورحمة الله وبركاته

أريد طلب: ${toolName}
السعر: ${price} ريال

من محل خياطة السيدة آيه 🌟`;

            const whatsappUrl = `https://wa.me/966561556965?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // تأثير الحركة عند التمرير
        window.addEventListener('scroll', () => {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                const cardTop = card.getBoundingClientRect().top;
                const cardVisible = 150;

                if (cardTop < window.innerHeight - cardVisible) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        });

        // تهيئة الكروت عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                card.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
