<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .buttons {
            display: grid;
            gap: 20px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid rgba(102, 126, 234, 0.2);
        }

        .feature {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
            color: #666;
            font-size: 1rem;
        }

        .feature i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .contact {
            margin-top: 30px;
            padding: 20px;
            background: rgba(37, 211, 102, 0.1);
            border-radius: 15px;
        }

        .contact h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .contact p {
            color: #666;
            font-size: 1.1rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-cut"></i>
        </div>
        
        <h1 class="title">محل خياطة السيدة آيه</h1>
        <p class="subtitle">أجمل الفساتين وأدوات الخياطة بأفضل الأسعار</p>
        
        <div class="buttons">
            <a href="app.html" class="btn btn-primary">
                <i class="fas fa-tshirt"></i>
                تصفح المنتجات
            </a>
            
            <a href="qr-complete.html" class="btn btn-secondary">
                <i class="fas fa-qrcode"></i>
                إنشاء QR Code
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <i class="fas fa-check-circle"></i>
                <span>6 فساتين راقية بألوان متنوعة</span>
            </div>
            <div class="feature">
                <i class="fas fa-check-circle"></i>
                <span>6 أدوات خياطة احترافية</span>
            </div>
            <div class="feature">
                <i class="fas fa-check-circle"></i>
                <span>طلب مباشر عبر واتساب</span>
            </div>
            <div class="feature">
                <i class="fas fa-check-circle"></i>
                <span>أسعار مناسبة للجميع</span>
            </div>
        </div>
        
        <div class="contact">
            <h3><i class="fas fa-phone"></i> للطلب والاستفسار:</h3>
            <p>📞 0561556965</p>
        </div>
    </div>
</body>
</html>
