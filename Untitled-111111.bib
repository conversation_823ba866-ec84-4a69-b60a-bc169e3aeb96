// هذا الملف يسمى main.dart وهو نقطة بداية التطبيق

import 'package:flutter/material.dart';

void main() {
  runApp(const WaslaApp());
}

// هذا هو التطبيق الرئيسي
class WaslaApp extends StatelessWidget {
  const WaslaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'وصلة',
      theme: ThemeData(
        primarySwatch: Colors.deepPurple,
        fontFamily: 'Tajawal', // استخدام خط عربي جميل مثل تجوال
        scaffoldBackgroundColor: Colors.grey[100],
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.black),
          titleTextStyle: TextStyle(
            color: Colors.black,
            fontSize: 22,
            fontWeight: FontWeight.bold,
            fontFamily: '<PERSON><PERSON><PERSON>',
          ),
        ),
      ),
      debugShowCheckedModeBanner: false,
      home: const HomeScreen(),
      // لدعم اللغة العربية من اليمين لليسار
      localizationsDelegates: const [
        // ... (add localization delegates here if needed)
      ],
      supportedLocales: const [
        Locale('ar', ''), // Arabic
      ],
    );
  }
}

// هذه هي الشاشة الرئيسية
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وصلة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // سيتم هنا برمجة وظيفة البحث
            },
          ),
          IconButton(
            icon: const Icon(Icons.shopping_cart_outlined),
            onPressed: () {
              // سيتم هنا برمجة الانتقال لسلة التسوق
            },
          ),
        ],
      ),
      body: ListView(
        children: [
          // 1. بطاقة المنتج المميز
          _buildFeaturedProductCard(),

          const SizedBox(height: 20),

          // 2. عنوان قسم "الأقسام"
          _buildSectionTitle('الأقسام'),

          // 3. قائمة الأقسام الأفقية
          _buildCategoriesList(),

          const SizedBox(height: 20),

          // 4. عنوان قسم "وصل حديثاً"
          _buildSectionTitle('وصل حديثاً'),

          // 5. شبكة المنتجات
          _buildProductsGrid(),
        ],
      ),
    );
  }

  // ويدجت لبناء بطاقة المنتج المميز
  Widget _buildFeaturedProductCard() {
    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: Colors.deepPurple,
        borderRadius: BorderRadius.circular(20),
        image: const DecorationImage(
          image: NetworkImage('https://images.unsplash.com/photo-1594385348824-a28a1c385b0d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=**********************************************************&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1080'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(Colors.black38, BlendMode.darken),
        ),
      ),
      height: 200,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'شاحن متنقل فائق السرعة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'خصم 30% لفترة محدودة!',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  // ويدجت لبناء عناوين الأقسام
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // ويدجت لبناء قائمة الأقسام
  Widget _buildCategoriesList() {
    final categories = [
      {'icon': Icons.phone_iphone, 'name': 'كفرات'},
      {'icon': Icons.power, 'name': 'شواحن'},
      {'icon': Icons.cable, 'name': 'كابلات'},
      {'icon': Icons.screen_share_outlined, 'name': 'حماية'},
      {'icon': Icons.headphones, 'name': 'سماعات'},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return Container(
            width: 80,
            margin: const EdgeInsets.only(right: 10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    categories[index]['icon'] as IconData,
                    color: Colors.deepPurple,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  categories[index]['name'] as String,
                  style: const TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // ويدجت لبناء شبكة المنتجات
  Widget _buildProductsGrid() {
    // هذه بيانات وهمية، في التطبيق الحقيقي ستأتي من قاعدة البيانات
    final products = List.generate(6, (index) => 'منتج ${index + 1}');

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(15),
                    ),
                    image: DecorationImage(
                      // استخدم صورة منتج حقيقية هنا
                      image: NetworkImage('https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/MPU63?wid=1144&hei=1144&fmt=jpeg&qlt=90&.v=1661472382745'),
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(products[index], style: const TextStyle(fontWeight: FontWeight.bold)),
                    const Text('SAR 99.00', style: TextStyle(color: Colors.grey)),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}