body {
  font-family: 'Tahoma', sans-serif;
  background: #f0f5f9;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
  color: #2c3e50;
  text-align: center;
}

.input-section {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

input {
  flex: 1;
  padding: 10px;
  border: 2px solid #3498db;
  border-radius: 5px;
  font-size: 16px;
}

button {
  padding: 15px 30px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

button:hover {
  background-color: #219a52;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0,0,0,0.2);
}

ul {
  list-style: none;
  padding: 0;
}

li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 5px 0;
  background: #f8f9fa;
  border-radius: 5px;
}

li:hover {
  background: #e9ecef;
}

.delete-btn {
  background: #e74c3c;
  padding: 5px 10px;
  border-radius: 3px;
}