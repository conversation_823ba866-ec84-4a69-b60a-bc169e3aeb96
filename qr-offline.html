<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 مولد QR Code - محل خياطة السيدة آيه</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .input-group label {
            display: block;
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        #urlInput {
            direction: ltr;
            text-align: left;
        }

        .qr-display {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-display canvas {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .qr-placeholder {
            color: #ccc;
            font-size: 1.1rem;
        }

        .qr-placeholder i {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }

        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .btn-social {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); }
        .btn-instagram { background: linear-gradient(135deg, #e1306c 0%, #fd1d1d 100%); }
        .btn-twitter { background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); }
        .btn-facebook { background: linear-gradient(135deg, #4267b2 0%, #365899 100%); }

        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        }

        .marketing-text {
            margin-top: 20px;
            padding: 20px;
            background: rgba(37, 211, 102, 0.1);
            border-radius: 15px;
            text-align: right;
        }

        .marketing-text h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .text-box {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .text-box:hover {
            background: #f0f8ff;
            transform: translateY(-2px);
        }

        .text-box p {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #25d366;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
            
            .social-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-qrcode"></i> مولد QR Code</h1>
            <p>محل خياطة السيدة آيه - يعمل بدون إنترنت</p>
        </div>

        <div class="input-group">
            <label for="urlInput"><i class="fas fa-link"></i> رابط التطبيق:</label>
            <input type="url" id="urlInput" value="">
        </div>

        <div class="input-group">
            <label for="qrSize"><i class="fas fa-expand-arrows-alt"></i> حجم QR Code:</label>
            <select id="qrSize">
                <option value="200">صغير - 200px</option>
                <option value="300" selected>متوسط - 300px</option>
                <option value="400">كبير - 400px</option>
            </select>
        </div>

        <div class="qr-display" id="qrcode">
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                اضغط "إنشاء QR Code" لإنشاء الكود
            </div>
        </div>

        <div class="buttons">
            <button type="button" class="btn btn-primary" onclick="generateQR()">
                <i class="fas fa-sync-alt"></i>
                إنشاء QR Code
            </button>
            <button type="button" class="btn btn-success" onclick="downloadQR()">
                <i class="fas fa-download"></i>
                تحميل QR Code
            </button>
        </div>

        <div class="social-buttons">
            <button type="button" class="btn-social btn-whatsapp" onclick="shareToWhatsApp()">
                <i class="fab fa-whatsapp"></i>
                واتساب
            </button>
            <button type="button" class="btn-social btn-instagram" onclick="shareToInstagram()">
                <i class="fab fa-instagram"></i>
                انستقرام
            </button>
            <button type="button" class="btn-social btn-twitter" onclick="shareToTwitter()">
                <i class="fab fa-twitter"></i>
                تويتر
            </button>
            <button type="button" class="btn-social btn-facebook" onclick="shareToFacebook()">
                <i class="fab fa-facebook"></i>
                فيسبوك
            </button>
        </div>

        <div class="marketing-text">
            <h3><i class="fas fa-edit"></i> نص تسويقي جاهز</h3>
            <div class="text-box" onclick="copyText(this)">
                <p>🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965</p>
            </div>
        </div>
    </div>

    <div id="notification" class="notification">
        تم بنجاح! ✅
    </div>

    <!-- مكتبة QR Code محلية -->
    <script>
        // مكتبة QR Code مبسطة ومحلية
        function QRCode(text, size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // حساب حجم الشبكة
            const gridSize = 25; // 25x25 grid
            const cellSize = size / gridSize;

            canvas.width = size;
            canvas.height = size;

            // خلفية بيضاء
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, size, size);

            // إنشاء نمط QR بسيط
            ctx.fillStyle = '#000000';

            // إنشاء نمط أساسي للـ QR Code
            const pattern = generateQRPattern(text, gridSize);

            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    if (pattern[row][col]) {
                        ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                    }
                }
            }

            return canvas;
        }

        function generateQRPattern(text, size) {
            const pattern = [];

            // إنشاء مصفوفة فارغة
            for (let i = 0; i < size; i++) {
                pattern[i] = new Array(size).fill(false);
            }

            // إضافة مربعات الزوايا (Finder patterns)
            addFinderPattern(pattern, 0, 0);
            addFinderPattern(pattern, 0, size - 7);
            addFinderPattern(pattern, size - 7, 0);

            // إضافة نمط البيانات بناءً على النص
            addDataPattern(pattern, text, size);

            return pattern;
        }

        function addFinderPattern(pattern, startRow, startCol) {
            const finderPattern = [
                [1,1,1,1,1,1,1],
                [1,0,0,0,0,0,1],
                [1,0,1,1,1,0,1],
                [1,0,1,1,1,0,1],
                [1,0,1,1,1,0,1],
                [1,0,0,0,0,0,1],
                [1,1,1,1,1,1,1]
            ];

            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    if (startRow + i < pattern.length && startCol + j < pattern[0].length) {
                        pattern[startRow + i][startCol + j] = finderPattern[i][j] === 1;
                    }
                }
            }
        }

        function addDataPattern(pattern, text, size) {
            // تحويل النص إلى hash بسيط
            let hash = 0;
            for (let i = 0; i < text.length; i++) {
                hash = ((hash << 5) - hash + text.charCodeAt(i)) & 0xffffffff;
            }

            // إضافة نمط البيانات
            for (let row = 9; row < size - 9; row++) {
                for (let col = 9; col < size - 9; col++) {
                    // تجنب مناطق الزوايا
                    if ((row < 9 || row >= size - 9) && (col < 9 || col >= size - 9)) {
                        continue;
                    }

                    // إنشاء نمط بناءً على الموقع والـ hash
                    const value = (hash + row * col + row + col) % 3;
                    pattern[row][col] = value === 0;
                }
            }

            // إضافة خطوط التوقيت
            for (let i = 8; i < size - 8; i++) {
                pattern[6][i] = i % 2 === 0;
                pattern[i][6] = i % 2 === 0;
            }
        }

        // الرابط الافتراضي
        const defaultUrl = window.location.href.replace('qr-offline.html', 'app.html');

        // تهيئة التطبيق
        window.addEventListener('DOMContentLoaded', () => {
            document.getElementById('urlInput').value = defaultUrl;
            generateQR(); // إنشاء QR Code تلقائياً
        });

        function generateQR() {
            const url = document.getElementById('urlInput').value || defaultUrl;
            const size = parseInt(document.getElementById('qrSize').value);
            const qrContainer = document.getElementById('qrcode');

            try {
                // إظهار رسالة التحميل
                qrContainer.innerHTML = `
                    <div class="qr-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري إنشاء QR Code...
                    </div>
                `;

                // إنشاء QR Code محلياً
                setTimeout(() => {
                    const canvas = QRCode(url, size);
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(canvas);
                    showNotification('تم إنشاء QR Code بنجاح! ✅');
                }, 500);

            } catch (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                qrContainer.innerHTML = `
                    <div class="qr-placeholder" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في إنشاء QR Code
                    </div>
                `;
                showNotification('خطأ في إنشاء QR Code! ❌');
            }
        }

        function downloadQR() {
            const canvas = document.querySelector('#qrcode canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = 'محل-خياطة-السيدة-آيه-QR.png';
                link.href = canvas.toDataURL('image/png');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showNotification('تم تحميل QR Code بنجاح! 📥');
            } else {
                showNotification('يرجى إنشاء QR Code أولاً! ⚠️');
            }
        }

        function shareToWhatsApp() {
            const text = `🌟 محل خياطة السيدة آيه 🌟
أجمل الفساتين بأفضل الأسعار!
👗 ألوان متنوعة وتصاميم عصرية
💰 أسعار مناسبة للجميع
📱 امسح الكود واطلب الآن 👇
📞 للطلب: 0561556965

${document.getElementById('urlInput').value}`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
            showNotification('تم فتح واتساب للمشاركة! 📱');
        }

        function shareToInstagram() {
            showNotification('احفظ QR Code ثم شاركه في قصتك على انستقرام! 📸');
            downloadQR();
        }

        function shareToTwitter() {
            const text = `🌟 محل خياطة السيدة آيه
👗 فساتين راقية بأسعار مميزة
📱 امسح الكود واطلب الآن!
#فساتين #خياطة #موضة

${document.getElementById('urlInput').value}`;

            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
            window.open(twitterUrl, '_blank');
            showNotification('تم فتح تويتر للمشاركة! 🐦');
        }

        function shareToFacebook() {
            const url = document.getElementById('urlInput').value;
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(facebookUrl, '_blank');
            showNotification('تم فتح فيسبوك للمشاركة! 📘');
        }

        function copyText(element) {
            const text = element.querySelector('p').textContent;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('تم نسخ النص التسويقي! 📋');
                    element.style.background = '#d4edda';
                    setTimeout(() => {
                        element.style.background = 'white';
                    }, 1000);
                }).catch(() => {
                    fallbackCopyText(text, element);
                });
            } else {
                fallbackCopyText(text, element);
            }
        }

        function fallbackCopyText(text, element) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showNotification('تم نسخ النص التسويقي! 📋');
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = 'white';
                }, 1000);
            } catch (err) {
                showNotification('فشل في نسخ النص! ❌');
            }
            document.body.removeChild(textArea);
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('qrSize').addEventListener('change', generateQR);
            document.getElementById('urlInput').addEventListener('input', function() {
                if (this.value.trim()) {
                    generateQR();
                }
            });
        });
    </script>
</body>
</html>
